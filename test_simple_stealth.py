#!/usr/bin/env python3
"""
简化的隐蔽模式测试
"""

import asyncio
import sys
import os
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import BaiduChatConfig
from playwright.async_api import async_playwright


async def test_simple_stealth():
    """简化的隐蔽模式测试"""
    print("🥷 简化隐蔽模式测试...")
    
    playwright = await async_playwright().start()
    
    try:
        # 启动浏览器（隐蔽模式参数）
        browser = await playwright.chromium.launch(
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-infobars'
            ]
        )
        
        # 创建上下文
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='zh-CN',
            timezone_id='Asia/Shanghai'
        )
        
        # 创建页面
        page = await context.new_page()
        
        # 注入反检测脚本
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
        
        print("✓ 浏览器初始化完成")
        
        # 直接访问聊天页面
        print("📱 访问百度聊天页面...")
        await page.goto('https://chat.baidu.com', timeout=60000)
        
        print("✓ 页面加载完成")
        
        # 等待页面稳定
        await asyncio.sleep(5)
        
        # 检查是否有验证码
        captcha_elements = await page.query_selector_all('.captcha, .verify, [class*="captcha"], [class*="verify"]')
        if captcha_elements:
            print("⚠️  检测到验证码元素")
        else:
            print("✅ 未检测到验证码")
        
        # 查找输入框
        input_selectors = [
            '#chat-input-box',
            '[contenteditable="true"]',
            '.cs-pc-input-custom',
            'textarea',
            'input[type="text"]'
        ]
        
        input_found = False
        for selector in input_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    print(f"✅ 找到输入框: {selector}")
                    
                    # 模拟真实用户行为
                    await element.click()
                    await asyncio.sleep(random.uniform(0.5, 1.0))
                    
                    # 输入测试消息
                    test_message = "你好"
                    for char in test_message:
                        await page.keyboard.type(char)
                        await asyncio.sleep(random.uniform(0.1, 0.3))
                    
                    print(f"✅ 输入消息: {test_message}")
                    
                    # 等待一下再发送
                    await asyncio.sleep(random.uniform(1.0, 2.0))
                    
                    # 发送消息
                    await page.keyboard.press('Enter')
                    print("✅ 消息已发送")
                    
                    input_found = True
                    break
                    
            except Exception as e:
                print(f"❌ 选择器 {selector} 失败: {e}")
                continue
        
        if not input_found:
            print("❌ 未找到输入框")
        
        # 等待观察结果
        print("⏳ 等待30秒观察结果...")
        await asyncio.sleep(30)
        
        # 截图保存
        await page.screenshot(path="stealth_test_result.png", full_page=True)
        print("📸 截图已保存: stealth_test_result.png")
        
        # 关闭浏览器
        await browser.close()
        await playwright.stop()
        
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            await browser.close()
            await playwright.stop()
        except:
            pass


def main():
    """主函数"""
    print("🥷 简化隐蔽模式测试")
    print("=" * 50)
    
    try:
        asyncio.run(test_simple_stealth())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    main()
