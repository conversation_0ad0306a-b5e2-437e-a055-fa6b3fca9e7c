#!/usr/bin/env python3
"""
测试隐蔽模式并获取AI回复
"""

import asyncio
import sys
import os
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_with_response():
    """测试并获取AI回复"""
    print("🤖 测试百度AI聊天并获取回复...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=60000,  # 60秒超时
        typing_delay=150  # 打字延迟
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 隐蔽模式浏览器初始化成功")
            
            # 测试消息
            test_message = "你好，请简单介绍一下你自己"
            print(f"📤 发送消息: {test_message}")
            
            try:
                # 发送消息并等待回复
                response = await bot.send_message_stealth(test_message)
                
                if response and response.strip():
                    print(f"\n🎉 成功获取AI回复:")
                    print("=" * 50)
                    print(response)
                    print("=" * 50)
                else:
                    print("❌ 未获取到有效回复")
                    
                    # 尝试手动查找回复
                    print("🔍 尝试手动查找页面上的回复...")
                    await asyncio.sleep(5)
                    
                    # 查找所有可能包含回复的元素
                    all_elements = await bot.page.query_selector_all('div')
                    print(f"📊 页面上共有 {len(all_elements)} 个div元素")
                    
                    # 查找包含文本的元素
                    text_elements = []
                    for element in all_elements:
                        try:
                            text = await element.inner_text()
                            if text and len(text.strip()) > 10:  # 只要有意义的文本
                                text_elements.append(text.strip())
                        except:
                            pass
                    
                    print(f"📝 找到 {len(text_elements)} 个包含文本的元素")
                    
                    # 显示最后几个文本元素（可能包含AI回复）
                    if text_elements:
                        print("\n🔍 最后几个文本元素:")
                        for i, text in enumerate(text_elements[-5:], 1):
                            print(f"[{i}] {text[:100]}{'...' if len(text) > 100 else ''}")
                
                # 获取对话历史
                history = bot.get_conversation_history()
                print(f"\n📚 对话历史记录数: {len(history)}")
                
                if history:
                    for i, conv in enumerate(history, 1):
                        print(f"\n对话 {i}:")
                        print(f"用户: {conv['user_message']}")
                        print(f"AI: {conv['ai_response']}")
                
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
                # 截图保存错误状态
                await bot.page.screenshot(path="error_screenshot.png", full_page=True)
                print("📸 错误截图已保存: error_screenshot.png")
                
                # 保存页面HTML
                content = await bot.page.content()
                with open("error_page.html", "w", encoding="utf-8") as f:
                    f.write(content)
                print("💾 页面HTML已保存: error_page.html")
                
                raise
            
            print("\n✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_interaction():
    """简单交互测试"""
    print("\n🔄 开始简单交互测试...")
    
    config = BaiduChatConfig(
        headless=False,
        debug=False,  # 减少日志输出
        response_timeout=30000
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            messages = ["你好", "今天天气怎么样？", "谢谢"]
            
            for i, message in enumerate(messages, 1):
                print(f"\n第{i}轮对话:")
                print(f"👤 用户: {message}")
                
                try:
                    response = await bot.send_message_stealth(message)
                    if response:
                        print(f"🤖 AI: {response}")
                    else:
                        print("🤖 AI: [无回复]")
                    
                    # 短暂等待
                    await asyncio.sleep(3)
                    
                except Exception as e:
                    print(f"❌ 第{i}轮对话失败: {e}")
                    break
            
            print("\n📊 最终对话历史:")
            history = bot.get_conversation_history()
            for i, conv in enumerate(history, 1):
                print(f"{i}. 用户: {conv['user_message']}")
                print(f"   AI: {conv['ai_response']}")
                
    except Exception as e:
        print(f"❌ 简单交互测试失败: {e}")


def main():
    """主函数"""
    print("🧪 百度AI聊天机器人回复测试")
    print("=" * 60)
    
    try:
        # 运行详细测试
        asyncio.run(test_with_response())
        
        # 运行简单交互测试
        # asyncio.run(test_simple_interaction())
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
