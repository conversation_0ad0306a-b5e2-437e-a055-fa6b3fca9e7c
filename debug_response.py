#!/usr/bin/env python3
"""
调试回复检测 - 查看页面上的回复元素
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.baidu_chat_bot import BaiduChatBot
from src.config import BaiduChatConfig


async def debug_response_detection():
    """调试回复检测"""
    print("🔍 调试回复检测...")
    
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=60000  # 60秒超时
    )
    
    try:
        async with BaiduChatBot(config) as bot:
            print("✓ 浏览器初始化成功")
            
            # 发送消息
            print("📤 发送消息: 你好")
            
            # 找到输入框并输入消息
            input_element = await bot.page.query_selector('#chat-input-box')
            if input_element:
                await input_element.click()
                await input_element.type("你好", delay=100)
                print("✓ 消息输入完成")
                
                # 发送消息
                await bot.page.keyboard.press('Enter')
                print("✓ 消息已发送")
                
                # 等待一段时间让AI回复
                print("⏳ 等待AI回复...")
                await asyncio.sleep(10)
                
                # 查找页面上的所有可能的回复元素
                print("\n🔍 查找页面上的回复元素...")
                
                # 扩展的回复选择器
                selectors_to_check = [
                    # 百度特定选择器
                    '.chat-item',
                    '.chat-content',
                    '.message-item',
                    '.answer-content',
                    '.chat-answer',
                    '.response-text',
                    '.ai-message',
                    '.bot-message',
                    
                    # 通用选择器
                    '.chat-message',
                    '.message',
                    '.response',
                    '.reply',
                    '.answer',
                    
                    # 属性选择器
                    '[data-role="assistant"]',
                    '[data-type="response"]',
                    '[data-message-type="ai"]'
                ]
                
                found_responses = []
                
                for selector in selectors_to_check:
                    try:
                        elements = await bot.page.query_selector_all(selector)
                        if elements:
                            print(f"✓ 找到 {len(elements)} 个元素: {selector}")
                            
                            for i, element in enumerate(elements):
                                try:
                                    text = await element.inner_text()
                                    if text and len(text.strip()) > 0:
                                        found_responses.append({
                                            'selector': selector,
                                            'index': i,
                                            'text': text.strip()[:100] + ('...' if len(text.strip()) > 100 else '')
                                        })
                                        print(f"  [{i}] 文本: {text.strip()[:100]}{'...' if len(text.strip()) > 100 else ''}")
                                except:
                                    pass
                    except:
                        pass
                
                if found_responses:
                    print(f"\n✅ 找到 {len(found_responses)} 个包含文本的元素")
                    
                    # 尝试找到最新的回复（通常是最后一个）
                    print("\n🎯 分析可能的AI回复:")
                    for resp in found_responses[-5:]:  # 显示最后5个
                        print(f"选择器: {resp['selector']}")
                        print(f"文本: {resp['text']}")
                        print("-" * 50)
                
                else:
                    print("❌ 未找到任何包含文本的回复元素")
                
                # 截图保存当前状态
                await bot.page.screenshot(path="debug_response_screenshot.png", full_page=True)
                print("📸 已保存调试截图: debug_response_screenshot.png")
                
                # 等待用户观察
                print("\n⏸️  浏览器将保持打开30秒，请观察页面...")
                await asyncio.sleep(30)
            
            else:
                print("❌ 未找到输入框")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🐛 回复检测调试工具")
    print("=" * 50)
    
    try:
        asyncio.run(debug_response_detection())
        print("\n✅ 调试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")


if __name__ == "__main__":
    main()
