#!/usr/bin/env python3
"""
测试改进后的AI回复检测
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_improved_response():
    """测试改进后的回复检测"""
    print("🔧 测试改进后的AI回复检测...")
    
    # 创建配置（使用WebKit，增加超时时间）
    config = BaiduChatConfig(
        browser_type="webkit",     # 使用WebKit
        headless=False,            # 显示浏览器窗口
        debug=True,                # 启用调试
        page_load_timeout=60000,   # 60秒页面加载超时
        response_timeout=120000,   # 120秒AI回复超时（增加到2分钟）
        element_timeout=60000,     # 60秒元素查找超时
        typing_delay=150           # 打字延迟
    )
    
    try:
        print("🚀 正在初始化WebKit浏览器...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ WebKit浏览器初始化成功")
            
            # 测试简单问题
            test_messages = [
                "你好",
                "1+1等于几？",
                "请简单介绍一下人工智能"
            ]
            
            for i, test_message in enumerate(test_messages, 1):
                print(f"\n📤 第{i}轮测试 - 发送消息: {test_message}")
                
                try:
                    # 发送消息并等待回复
                    response = await bot.send_message_stealth(test_message)
                    
                    if response and response.strip():
                        print(f"🎉 成功获取AI回复:")
                        print("=" * 60)
                        print(f"👤 用户: {test_message}")
                        print(f"🤖 AI: {response}")
                        print("=" * 60)
                        
                        # 检查回复质量
                        if len(response) > 20 and "思考中" not in response:
                            print("✅ 回复质量良好")
                        else:
                            print("⚠️  回复可能不完整")
                        
                    else:
                        print("❌ 未获取到有效回复")
                    
                    # 短暂等待再进行下一轮
                    if i < len(test_messages):
                        print("⏳ 等待3秒后进行下一轮测试...")
                        await asyncio.sleep(3)
                    
                except Exception as e:
                    print(f"❌ 第{i}轮测试失败: {e}")
                    continue
            
            # 获取完整对话历史
            history = bot.get_conversation_history()
            print(f"\n📚 完整对话历史 ({len(history)} 条):")
            
            for i, conv in enumerate(history, 1):
                print(f"\n对话 {i}:")
                print(f"  👤 用户: {conv['user_message']}")
                print(f"  🤖 AI: {conv['ai_response'][:100]}{'...' if len(conv['ai_response']) > 100 else ''}")
                print(f"  🕐 时间: {conv['timestamp']}")
            
            print("\n✅ 改进后的回复检测测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_single_detailed():
    """单次详细测试"""
    print("\n🔍 单次详细测试...")
    
    config = BaiduChatConfig(
        browser_type="webkit",
        headless=False,
        debug=True,
        response_timeout=90000  # 90秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            test_message = "请用一句话介绍什么是人工智能"
            print(f"📤 发送详细测试消息: {test_message}")
            
            response = await bot.send_message_stealth(test_message)
            
            print(f"\n📊 详细分析:")
            print(f"回复长度: {len(response)} 字符")
            print(f"是否包含'思考中': {'是' if '思考中' in response else '否'}")
            print(f"是否为有效回复: {'是' if len(response) > 10 and '思考中' not in response else '否'}")
            
            if response:
                print(f"\n完整回复内容:")
                print("-" * 60)
                print(response)
                print("-" * 60)
            
    except Exception as e:
        print(f"❌ 单次详细测试失败: {e}")


def main():
    """主函数"""
    print("🧪 改进后的AI回复检测测试")
    print("=" * 60)
    
    try:
        # 运行改进测试
        asyncio.run(test_improved_response())
        
        # 可选：运行单次详细测试
        # print("\n" + "="*60)
        # asyncio.run(test_single_detailed())
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
