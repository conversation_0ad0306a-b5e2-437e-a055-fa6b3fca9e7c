"""
命令行接口模块
"""
import argparse
import asyncio
import sys
import json
from typing import Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn

from .baidu_chat_bot import BaiduChatBot
from .stealth_bot import StealthBaiduChatBot
from .config import BaiduChatConfig
from .message_handler import MessageHandler


class ChatCLI:
    """聊天命令行接口类"""
    
    def __init__(self):
        self.console = Console()
        self.bot: Optional[BaiduChatBot] = None
        self.config: Optional[BaiduChatConfig] = None
    
    def create_config_from_args(self, args) -> BaiduChatConfig:
        """从命令行参数创建配置"""
        config = BaiduChatConfig()
        
        if args.browser:
            config.browser_type = args.browser
        if args.headless:
            config.headless = True
        if args.timeout:
            config.response_timeout = args.timeout * 1000
        if args.debug:
            config.debug = True
        if hasattr(args, 'stealth') and args.stealth:
            config.stealth_mode = True

        return config
    
    async def single_message_mode(self, message: str, config: BaiduChatConfig):
        """单次消息模式"""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                
                # 初始化
                task = progress.add_task("正在初始化浏览器...", total=None)

                # 选择机器人类型
                bot_class = StealthBaiduChatBot if getattr(config, 'stealth_mode', False) else BaiduChatBot
                send_method = 'send_message_stealth' if getattr(config, 'stealth_mode', False) else 'send_message'

                async with bot_class(config) as bot:
                    progress.update(task, description="正在发送消息...")

                    # 发送消息
                    response = await getattr(bot, send_method)(message)
                    
                    progress.update(task, description="完成", completed=True)
            
            # 显示结果
            self._display_response(message, response)
            
        except Exception as e:
            self.console.print(f"[red]错误: {e}[/red]")
            sys.exit(1)
    
    async def interactive_mode(self, config: BaiduChatConfig):
        """交互式聊天模式"""
        self.console.print(Panel.fit(
            "[bold blue]百度AI聊天机器人[/bold blue]\n"
            "输入 'quit' 或 'exit' 退出\n"
            "输入 'clear' 清空对话历史\n"
            "输入 'history' 查看对话历史",
            title="欢迎使用"
        ))
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("正在初始化浏览器...", total=None)

                # 选择机器人类型
                bot_class = StealthBaiduChatBot if getattr(config, 'stealth_mode', False) else BaiduChatBot
                send_method = 'send_message_stealth' if getattr(config, 'stealth_mode', False) else 'send_message'

                async with bot_class(config) as bot:
                    progress.update(task, description="初始化完成", completed=True)
                    
                    while True:
                        try:
                            # 获取用户输入
                            user_input = Prompt.ask("\n[bold green]你[/bold green]")
                            
                            if not user_input.strip():
                                continue
                            
                            # 处理特殊命令
                            if user_input.lower() in ['quit', 'exit', 'q']:
                                self.console.print("[yellow]再见![/yellow]")
                                break
                            elif user_input.lower() == 'clear':
                                bot.clear_conversation_history()
                                self.console.print("[yellow]对话历史已清空[/yellow]")
                                continue
                            elif user_input.lower() == 'history':
                                self._display_history(bot.get_conversation_history())
                                continue
                            
                            # 发送消息
                            with Progress(
                                SpinnerColumn(),
                                TextColumn("AI正在思考..."),
                                console=self.console
                            ) as msg_progress:
                                msg_task = msg_progress.add_task("", total=None)
                                response = await getattr(bot, send_method)(user_input)
                                msg_progress.update(msg_task, completed=True)
                            
                            # 显示回复
                            self._display_ai_response(response)
                            
                        except KeyboardInterrupt:
                            self.console.print("\n[yellow]用户中断，正在退出...[/yellow]")
                            break
                        except Exception as e:
                            self.console.print(f"[red]发送消息时出错: {e}[/red]")
                            continue
        
        except Exception as e:
            self.console.print(f"[red]初始化失败: {e}[/red]")
            sys.exit(1)
    
    def _display_response(self, user_message: str, ai_response: str):
        """显示单次对话结果"""
        # 显示用户消息
        self.console.print(Panel(
            user_message,
            title="[bold green]你的消息[/bold green]",
            border_style="green"
        ))
        
        # 显示AI回复
        self.console.print(Panel(
            ai_response,
            title="[bold blue]AI回复[/bold blue]",
            border_style="blue"
        ))
    
    def _display_ai_response(self, response: str):
        """显示AI回复"""
        self.console.print(Panel(
            response,
            title="[bold blue]AI[/bold blue]",
            border_style="blue"
        ))
    
    def _display_history(self, history: list):
        """显示对话历史"""
        if not history:
            self.console.print("[yellow]暂无对话历史[/yellow]")
            return
        
        self.console.print(Panel.fit(
            f"共有 {len(history)} 条对话记录",
            title="对话历史"
        ))
        
        for i, conversation in enumerate(history, 1):
            timestamp = conversation.get('timestamp', '未知时间')
            user_msg = conversation.get('user_message', '')
            ai_msg = conversation.get('ai_response', '')
            
            self.console.print(f"\n[bold]第 {i} 条对话[/bold] ({timestamp[:19]})")
            self.console.print(f"[green]你:[/green] {user_msg}")
            self.console.print(f"[blue]AI:[/blue] {ai_msg}")
    
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(
            description="百度AI聊天机器人命令行工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 单次查询
  python main.py --message "你好，请介绍一下人工智能"
  
  # 交互式聊天
  python main.py --interactive
  
  # 使用Firefox浏览器
  python main.py --browser firefox --interactive
  
  # 无头模式运行
  python main.py --headless --message "Hello"
            """
        )
        
        # 消息相关参数
        parser.add_argument(
            '-m', '--message',
            type=str,
            help='要发送的消息（单次查询模式）'
        )
        
        parser.add_argument(
            '-i', '--interactive',
            action='store_true',
            help='启动交互式聊天模式'
        )
        
        # 浏览器配置
        parser.add_argument(
            '--browser',
            choices=['chromium', 'firefox', 'webkit'],
            default='chromium',
            help='选择浏览器类型 (默认: chromium)'
        )
        
        parser.add_argument(
            '--headless',
            action='store_true',
            help='无头模式运行（不显示浏览器窗口）'
        )
        
        # 超时配置
        parser.add_argument(
            '--timeout',
            type=int,
            default=60,
            help='AI回复超时时间（秒，默认60秒）'
        )
        
        # 调试选项
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )

        parser.add_argument(
            '--stealth',
            action='store_true',
            help='启用隐蔽模式（绕过反爬虫检测）'
        )
        
        parser.add_argument(
            '--version',
            action='version',
            version='百度AI聊天机器人 v1.0.0'
        )
        
        return parser.parse_args()
    
    async def run(self):
        """运行CLI应用"""
        args = self.parse_arguments()
        
        # 验证参数
        if not args.message and not args.interactive:
            self.console.print("[red]错误: 请指定 --message 或 --interactive 参数[/red]")
            sys.exit(1)
        
        if args.message and args.interactive:
            self.console.print("[red]错误: --message 和 --interactive 不能同时使用[/red]")
            sys.exit(1)
        
        # 创建配置
        config = self.create_config_from_args(args)
        
        # 运行相应模式
        if args.message:
            await self.single_message_mode(args.message, config)
        else:
            await self.interactive_mode(config)


def main():
    """主函数"""
    cli = ChatCLI()
    try:
        asyncio.run(cli.run())
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
