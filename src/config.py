"""
配置管理模块
"""
import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class BaiduChatConfig:
    """百度聊天配置类"""
    
    # 基础配置
    base_url: str = "https://chat.baidu.com"
    browser_type: str = "chromium"  # chromium, firefox, webkit
    headless: bool = False
    
    # 超时配置
    page_load_timeout: int = 30000  # 页面加载超时(毫秒)
    response_timeout: int = 60000   # AI回复超时(毫秒)
    element_timeout: int = 10000    # 元素查找超时(毫秒)
    
    # 延迟配置
    typing_delay: int = 100         # 打字延迟(毫秒)
    wait_after_send: int = 2000     # 发送后等待时间(毫秒)
    
    # 重试配置
    max_retries: int = 3
    retry_delay: int = 1000
    
    # 浏览器配置
    viewport_width: int = 1280
    viewport_height: int = 720
    user_agent: Optional[str] = None
    
    # 调试配置
    debug: bool = False
    save_screenshots: bool = False
    screenshot_dir: str = "screenshots"
    
    @classmethod
    def from_env(cls) -> 'BaiduChatConfig':
        """从环境变量创建配置"""
        return cls(
            base_url=os.getenv('BAIDU_CHAT_URL', cls.base_url),
            browser_type=os.getenv('BROWSER_TYPE', cls.browser_type),
            headless=os.getenv('HEADLESS', 'false').lower() == 'true',
            page_load_timeout=int(os.getenv('PAGE_LOAD_TIMEOUT', cls.page_load_timeout)),
            response_timeout=int(os.getenv('RESPONSE_TIMEOUT', cls.response_timeout)),
            debug=os.getenv('DEBUG', 'false').lower() == 'true',
        )


# 默认配置实例
DEFAULT_CONFIG = BaiduChatConfig()
