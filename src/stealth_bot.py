"""
隐蔽模式聊天机器人 - 绕过反爬虫检测
"""
import asyncio
import random
import logging
from typing import Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from .config import BaiduChatConfig, DEFAULT_CONFIG
from .message_handler import MessageHandler


class StealthBaiduChatBot:
    """隐蔽模式百度聊天机器人"""
    
    def __init__(self, config: Optional[BaiduChatConfig] = None):
        self.config = config or DEFAULT_CONFIG
        self.message_handler = MessageHandler()
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.is_initialized = False
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        if self.config.debug:
            logging.basicConfig(level=logging.DEBUG)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化浏览器和页面（隐蔽模式）"""
        try:
            self.logger.info("正在初始化隐蔽模式浏览器...")
            
            # 启动playwright
            self.playwright = await async_playwright().start()
            
            # 选择浏览器类型
            if self.config.browser_type == "firefox":
                browser_launcher = self.playwright.firefox
            elif self.config.browser_type == "webkit":
                browser_launcher = self.playwright.webkit
            else:
                browser_launcher = self.playwright.chromium
            
            # 启动浏览器（隐蔽模式参数）
            self.browser = await browser_launcher.launch(
                headless=self.config.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',  # 禁用自动化控制标识
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-infobars',
                    '--window-size=1280,720'
                ]
            )
            
            # 创建浏览器上下文（模拟真实用户）
            context_options = {
                'viewport': {
                    'width': 1280 + random.randint(-50, 50),  # 随机化视口大小
                    'height': 720 + random.randint(-50, 50)
                },
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'locale': 'zh-CN',
                'timezone_id': 'Asia/Shanghai',
                'permissions': ['geolocation'],
                'extra_http_headers': {
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            }
            
            self.context = await self.browser.new_context(**context_options)
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 注入反检测脚本
            await self._inject_stealth_scripts()
            
            # 设置超时
            self.page.set_default_timeout(self.config.element_timeout)
            
            # 导航到百度聊天页面
            await self._navigate_to_chat_page_stealth()
            
            self.is_initialized = True
            self.logger.info("隐蔽模式浏览器初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            await self.close()
            raise
    
    async def _inject_stealth_scripts(self):
        """注入反检测脚本"""
        stealth_script = """
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 修改plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 修改languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        // 隐藏自动化痕迹
        window.chrome = {
            runtime: {},
        };
        
        // 模拟真实的权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Cypress ? 'denied' : 'granted' }) :
                originalQuery(parameters)
        );
        
        // 覆盖Date对象以避免时区检测
        const originalDate = Date;
        function MockDate(...args) {
            if (args.length === 0) {
                return new originalDate();
            }
            return new originalDate(...args);
        }
        MockDate.now = originalDate.now;
        MockDate.parse = originalDate.parse;
        MockDate.UTC = originalDate.UTC;
        MockDate.prototype = originalDate.prototype;
        window.Date = MockDate;
        """
        
        await self.page.add_init_script(stealth_script)
    
    async def _navigate_to_chat_page_stealth(self):
        """隐蔽模式导航到聊天页面"""
        self.logger.info(f"正在访问 {self.config.base_url}")
        
        try:
            # 模拟真实用户行为：先访问百度首页
            await self.page.goto('https://www.baidu.com', wait_until='domcontentloaded')
            await self._random_delay(2, 4)
            
            # 模拟鼠标移动
            await self._simulate_mouse_movement()
            
            # 再访问聊天页面
            await self.page.goto(
                self.config.base_url,
                wait_until='domcontentloaded',
                timeout=self.config.page_load_timeout
            )
            
            # 等待页面加载
            await self._random_delay(3, 6)
            
            # 模拟用户浏览行为
            await self._simulate_browsing_behavior()
            
            # 等待聊天界面元素出现
            await self._wait_for_chat_interface_stealth()
            
            self.logger.info("成功访问聊天页面")
            
        except Exception as e:
            self.logger.error(f"访问聊天页面失败: {e}")
            raise
    
    async def _simulate_mouse_movement(self):
        """模拟真实的鼠标移动"""
        for _ in range(random.randint(2, 5)):
            x = random.randint(100, 1000)
            y = random.randint(100, 600)
            await self.page.mouse.move(x, y)
            await self._random_delay(0.1, 0.3)
    
    async def _simulate_browsing_behavior(self):
        """模拟真实的浏览行为"""
        # 随机滚动
        for _ in range(random.randint(1, 3)):
            await self.page.mouse.wheel(0, random.randint(100, 300))
            await self._random_delay(0.5, 1.5)
        
        # 模拟点击页面空白区域
        await self.page.click('body', position={'x': 500, 'y': 300})
        await self._random_delay(1, 2)
    
    async def _random_delay(self, min_seconds: float, max_seconds: float):
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def _wait_for_chat_interface_stealth(self):
        """等待聊天界面加载完成（隐蔽模式）"""
        input_selectors = [
            '#chat-input-box',
            '[contenteditable="true"]',
            '.cs-pc-input-custom'
        ]
        
        for selector in input_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=10000)
                self.logger.debug(f"找到输入框: {selector}")
                return
            except:
                continue
        
        # 如果都没找到，等待一段时间后再试
        await self._random_delay(3, 5)
        self.logger.warning("未能立即找到输入框，将在发送消息时重试")
    
    async def send_message_stealth(self, message: str) -> str:
        """隐蔽模式发送消息"""
        if not self.is_initialized:
            raise RuntimeError("聊天机器人未初始化，请先调用 initialize()")
        
        # 验证消息
        is_valid, error_msg = self.message_handler.validate_message(message)
        if not is_valid:
            raise ValueError(f"消息验证失败: {error_msg}")
        
        # 清理消息
        clean_message = self.message_handler.clean_input_message(message)
        
        try:
            self.logger.info(f"正在发送消息: {clean_message[:50]}...")
            
            # 检查是否有验证码
            if await self._check_captcha():
                self.logger.warning("检测到验证码，尝试处理...")
                await self._handle_captcha()
            
            # 模拟真实用户行为发送消息
            await self._input_message_stealth(clean_message)
            await self._click_send_button_stealth()
            
            # 等待并获取回复
            response = await self._wait_for_response_stealth()
            
            # 处理回复
            clean_response = self.message_handler.extract_response_from_element(response)
            
            # 添加到历史记录
            self.message_handler.add_to_history(clean_message, clean_response)
            
            self.logger.info(f"收到回复: {clean_response[:50]}...")
            
            return clean_response
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            raise
    
    async def _check_captcha(self) -> bool:
        """检查是否有验证码"""
        captcha_selectors = [
            '.captcha',
            '.verify',
            '.security',
            '[class*="captcha"]',
            '[class*="verify"]',
            'iframe[src*="captcha"]'
        ]
        
        for selector in captcha_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    return True
            except:
                continue
        
        return False
    
    async def _handle_captcha(self):
        """处理验证码（简单策略）"""
        self.logger.info("检测到验证码，等待用户手动处理...")
        
        # 等待验证码消失或用户手动处理
        for _ in range(30):  # 最多等待30秒
            await asyncio.sleep(1)
            if not await self._check_captcha():
                self.logger.info("验证码已处理完成")
                return
        
        raise RuntimeError("验证码处理超时")
    
    async def _input_message_stealth(self, message: str):
        """隐蔽模式输入消息"""
        input_selectors = [
            '#chat-input-box',
            '[contenteditable="true"]',
            '.cs-pc-input-custom'
        ]
        
        input_element = None
        for selector in input_selectors:
            try:
                input_element = await self.page.wait_for_selector(selector, timeout=3000)
                if input_element:
                    break
            except:
                continue
        
        if not input_element:
            raise RuntimeError("无法找到输入框")
        
        # 模拟真实用户点击
        await input_element.click()
        await self._random_delay(0.5, 1.0)
        
        # 清空输入框
        await self.page.keyboard.press('Control+a')
        await self.page.keyboard.press('Delete')
        await self._random_delay(0.2, 0.5)
        
        # 模拟真实打字（随机延迟）
        for char in message:
            await self.page.keyboard.type(char)
            # 随机打字延迟
            delay = random.uniform(0.05, 0.2)
            await asyncio.sleep(delay)
        
        self.logger.debug("消息输入完成")
    
    async def _click_send_button_stealth(self):
        """隐蔽模式点击发送按钮"""
        # 模拟真实用户的发送行为
        await self._random_delay(0.5, 1.5)
        
        # 尝试Enter键发送
        await self.page.keyboard.press('Enter')
        await self._random_delay(1, 2)
        
        self.logger.debug("使用Enter键发送消息")
    
    async def _wait_for_response_stealth(self) -> str:
        """隐蔽模式等待AI回复"""
        response_selectors = [
            '.chat-item:last-child .chat-content',
            '.chat-item:last-child',
            '.message-item:last-child',
            '.answer-content',
            '.chat-answer'
        ]
        
        start_time = asyncio.get_event_loop().time()
        timeout = self.config.response_timeout / 1000
        
        last_response_text = ""
        stable_count = 0
        
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            try:
                response_element = None
                for selector in response_selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        if elements:
                            response_element = elements[-1]
                            break
                    except:
                        continue
                
                if response_element:
                    current_text = await response_element.inner_text()
                    current_text = current_text.strip()
                    
                    if current_text and current_text != last_response_text:
                        last_response_text = current_text
                        stable_count = 0
                    elif current_text == last_response_text and current_text:
                        stable_count += 1
                        
                        if stable_count >= 3:
                            self.logger.debug("检测到回复完成")
                            return current_text
                
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.debug(f"等待回复时出错: {e}")
                await asyncio.sleep(1)
        
        if last_response_text:
            self.logger.warning("等待回复超时，返回最后获取的内容")
            return last_response_text
        
        raise TimeoutError("等待AI回复超时")
    
    async def close(self):
        """关闭浏览器和清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            self.is_initialized = False
            self.logger.info("浏览器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {e}")
    
    def get_conversation_history(self):
        """获取对话历史"""
        return self.message_handler.get_conversation_history()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.message_handler.clear_history()
