"""
消息处理器模块
"""
import re
import html
from typing import List, Optional
from datetime import datetime


class MessageHandler:
    """消息处理器类"""
    
    def __init__(self):
        self.conversation_history: List[dict] = []
    
    def clean_input_message(self, message: str) -> str:
        """清理输入消息"""
        if not message:
            return ""
        
        # 去除首尾空白
        message = message.strip()
        
        # 限制消息长度
        if len(message) > 2000:
            message = message[:2000] + "..."
        
        return message
    
    def clean_response_message(self, response: str) -> str:
        """清理AI回复消息"""
        if not response:
            return ""
        
        # HTML解码
        response = html.unescape(response)
        
        # 去除多余的空白字符
        response = re.sub(r'\s+', ' ', response)
        response = response.strip()
        
        # 去除可能的markdown标记（如果有的话）
        response = re.sub(r'\*\*(.*?)\*\*', r'\1', response)  # 粗体
        response = re.sub(r'\*(.*?)\*', r'\1', response)      # 斜体
        
        return response
    
    def extract_response_from_element(self, element_text: str) -> str:
        """从页面元素中提取回复内容"""
        if not element_text:
            return ""
        
        # 去除可能的时间戳
        text = re.sub(r'\d{2}:\d{2}', '', element_text)
        
        # 去除可能的用户名或标识
        text = re.sub(r'^(AI|助手|小度)[:：]\s*', '', text, flags=re.IGNORECASE)
        
        return self.clean_response_message(text)
    
    def add_to_history(self, user_message: str, ai_response: str):
        """添加对话到历史记录"""
        conversation = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'ai_response': ai_response
        }
        self.conversation_history.append(conversation)
    
    def get_conversation_history(self) -> List[dict]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
    
    def format_response_for_display(self, response: str, include_timestamp: bool = True) -> str:
        """格式化回复用于显示"""
        if not response:
            return "AI: [无回复]"
        
        formatted = f"AI: {response}"
        
        if include_timestamp:
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted = f"[{timestamp}] {formatted}"
        
        return formatted
    
    def validate_message(self, message: str) -> tuple[bool, Optional[str]]:
        """验证消息是否有效"""
        if not message or not message.strip():
            return False, "消息不能为空"
        
        if len(message.strip()) < 1:
            return False, "消息太短"
        
        if len(message) > 2000:
            return False, "消息太长，请控制在2000字符以内"
        
        # 检查是否包含恶意内容（简单检查）
        forbidden_patterns = [
            r'<script.*?>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
        ]
        
        for pattern in forbidden_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return False, "消息包含不允许的内容"
        
        return True, None
