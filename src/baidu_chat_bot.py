"""
百度聊天机器人核心模块
"""
import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from .config import BaiduChatConfig, DEFAULT_CONFIG
from .message_handler import MessageHandler


class BaiduChatBot:
    """百度聊天机器人类"""
    
    def __init__(self, config: Optional[BaiduChatConfig] = None):
        self.config = config or DEFAULT_CONFIG
        self.message_handler = MessageHandler()
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.is_initialized = False
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        if self.config.debug:
            logging.basicConfig(level=logging.DEBUG)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化浏览器和页面"""
        try:
            self.logger.info("正在初始化浏览器...")
            
            # 启动playwright
            self.playwright = await async_playwright().start()
            
            # 选择浏览器类型
            if self.config.browser_type == "firefox":
                browser_launcher = self.playwright.firefox
            elif self.config.browser_type == "webkit":
                browser_launcher = self.playwright.webkit
            else:
                browser_launcher = self.playwright.chromium
            
            # 启动浏览器
            self.browser = await browser_launcher.launch(
                headless=self.config.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage'] if self.config.headless else []
            )
            
            # 创建浏览器上下文
            context_options = {
                'viewport': {
                    'width': self.config.viewport_width,
                    'height': self.config.viewport_height
                }
            }
            
            if self.config.user_agent:
                context_options['user_agent'] = self.config.user_agent
            
            self.context = await self.browser.new_context(**context_options)
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(self.config.element_timeout)
            
            # 导航到百度聊天页面
            await self._navigate_to_chat_page()
            
            self.is_initialized = True
            self.logger.info("浏览器初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            await self.close()
            raise
    
    async def _navigate_to_chat_page(self):
        """导航到聊天页面"""
        self.logger.info(f"正在访问 {self.config.base_url}")
        
        try:
            # 访问页面
            await self.page.goto(
                self.config.base_url,
                wait_until='domcontentloaded',
                timeout=self.config.page_load_timeout
            )
            
            # 等待页面完全加载
            await self.page.wait_for_load_state('networkidle', timeout=self.config.page_load_timeout)
            
            # 等待聊天界面元素出现
            await self._wait_for_chat_interface()
            
            self.logger.info("成功访问聊天页面")
            
        except Exception as e:
            self.logger.error(f"访问聊天页面失败: {e}")
            raise
    
    async def _wait_for_chat_interface(self):
        """等待聊天界面加载完成"""
        # 尝试多种可能的输入框选择器
        input_selectors = [
            'textarea[placeholder*="请输入"]',
            'textarea[placeholder*="输入"]',
            'input[placeholder*="请输入"]',
            'input[placeholder*="输入"]',
            '.chat-input textarea',
            '.input-box textarea',
            '#chat-input',
            '[data-testid="chat-input"]'
        ]
        
        for selector in input_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=5000)
                self.logger.debug(f"找到输入框: {selector}")
                return
            except:
                continue
        
        # 如果都没找到，等待一段时间后再试
        await asyncio.sleep(3)
        self.logger.warning("未能立即找到输入框，将在发送消息时重试")
    
    async def send_message(self, message: str) -> str:
        """发送消息并获取回复"""
        if not self.is_initialized:
            raise RuntimeError("聊天机器人未初始化，请先调用 initialize()")
        
        # 验证消息
        is_valid, error_msg = self.message_handler.validate_message(message)
        if not is_valid:
            raise ValueError(f"消息验证失败: {error_msg}")
        
        # 清理消息
        clean_message = self.message_handler.clean_input_message(message)
        
        try:
            self.logger.info(f"正在发送消息: {clean_message[:50]}...")
            
            # 查找并填写输入框
            await self._input_message(clean_message)
            
            # 发送消息
            await self._click_send_button()
            
            # 等待并获取回复
            response = await self._wait_for_response()
            
            # 处理回复
            clean_response = self.message_handler.extract_response_from_element(response)
            
            # 添加到历史记录
            self.message_handler.add_to_history(clean_message, clean_response)
            
            self.logger.info(f"收到回复: {clean_response[:50]}...")
            
            return clean_response
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            raise

    async def _input_message(self, message: str):
        """在输入框中输入消息"""
        input_selectors = [
            'textarea[placeholder*="请输入"]',
            'textarea[placeholder*="输入"]',
            'input[placeholder*="请输入"]',
            'input[placeholder*="输入"]',
            '.chat-input textarea',
            '.input-box textarea',
            '#chat-input',
            '[data-testid="chat-input"]',
            'textarea',
            'input[type="text"]'
        ]

        input_element = None
        for selector in input_selectors:
            try:
                input_element = await self.page.wait_for_selector(selector, timeout=3000)
                if input_element:
                    break
            except:
                continue

        if not input_element:
            raise RuntimeError("无法找到输入框")

        # 清空输入框
        await input_element.clear()

        # 输入消息（模拟真实打字）
        await input_element.type(message, delay=self.config.typing_delay)

        self.logger.debug("消息输入完成")

    async def _click_send_button(self):
        """点击发送按钮"""
        send_selectors = [
            'button[type="submit"]',
            'button:has-text("发送")',
            'button:has-text("Send")',
            '.send-button',
            '.chat-send',
            '[data-testid="send-button"]',
            'button:last-child',
            'svg[data-icon="send"]',
            '.icon-send'
        ]

        # 首先尝试键盘发送（Enter键）
        try:
            await self.page.keyboard.press('Enter')
            await asyncio.sleep(self.config.wait_after_send / 1000)
            self.logger.debug("使用Enter键发送消息")
            return
        except:
            pass

        # 如果Enter键不行，尝试点击发送按钮
        for selector in send_selectors:
            try:
                send_button = await self.page.wait_for_selector(selector, timeout=2000)
                if send_button:
                    await send_button.click()
                    await asyncio.sleep(self.config.wait_after_send / 1000)
                    self.logger.debug(f"使用按钮发送消息: {selector}")
                    return
            except:
                continue

        # 最后尝试Ctrl+Enter
        try:
            await self.page.keyboard.press('Control+Enter')
            await asyncio.sleep(self.config.wait_after_send / 1000)
            self.logger.debug("使用Ctrl+Enter发送消息")
        except:
            raise RuntimeError("无法找到发送按钮或发送方式")

    async def _wait_for_response(self) -> str:
        """等待AI回复"""
        # 可能的回复区域选择器
        response_selectors = [
            '.chat-message:last-child',
            '.message:last-child',
            '.ai-response:last-child',
            '.response:last-child',
            '[data-role="assistant"]:last-child',
            '.chat-bubble:last-child',
            '.message-content:last-child'
        ]

        start_time = asyncio.get_event_loop().time()
        timeout = self.config.response_timeout / 1000

        last_response_text = ""
        stable_count = 0

        while (asyncio.get_event_loop().time() - start_time) < timeout:
            try:
                # 尝试找到回复元素
                response_element = None
                for selector in response_selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        if elements:
                            response_element = elements[-1]  # 取最后一个
                            break
                    except:
                        continue

                if response_element:
                    current_text = await response_element.inner_text()
                    current_text = current_text.strip()

                    # 检查文本是否稳定（AI是否完成回复）
                    if current_text and current_text != last_response_text:
                        last_response_text = current_text
                        stable_count = 0
                    elif current_text == last_response_text and current_text:
                        stable_count += 1

                        # 如果文本稳定超过3次检查，认为回复完成
                        if stable_count >= 3:
                            self.logger.debug("检测到回复完成")
                            return current_text

                await asyncio.sleep(1)  # 每秒检查一次

            except Exception as e:
                self.logger.debug(f"等待回复时出错: {e}")
                await asyncio.sleep(1)

        # 超时后返回最后获取到的文本
        if last_response_text:
            self.logger.warning("等待回复超时，返回最后获取的内容")
            return last_response_text

        raise TimeoutError("等待AI回复超时")

    async def close(self):
        """关闭浏览器和清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            self.is_initialized = False
            self.logger.info("浏览器已关闭")

        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {e}")

    async def take_screenshot(self, filename: Optional[str] = None) -> str:
        """截图保存"""
        if not self.page:
            raise RuntimeError("页面未初始化")

        if not filename:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        screenshot_path = f"{self.config.screenshot_dir}/{filename}"
        await self.page.screenshot(path=screenshot_path, full_page=True)

        return screenshot_path

    def get_conversation_history(self):
        """获取对话历史"""
        return self.message_handler.get_conversation_history()

    def clear_conversation_history(self):
        """清空对话历史"""
        self.message_handler.clear_history()
