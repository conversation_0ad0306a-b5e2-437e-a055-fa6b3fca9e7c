#!/usr/bin/env python3
"""
调试AI回复元素定位
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from playwright.async_api import async_playwright


async def debug_response_elements():
    """调试AI回复元素"""
    print("🔍 调试AI回复元素定位...")
    
    playwright = await async_playwright().start()
    
    try:
        # 启动WebKit浏览器
        browser = await playwright.webkit.launch(headless=False)
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        )
        page = await context.new_page()
        
        print("✅ 浏览器启动成功")
        
        # 访问页面
        await page.goto('https://chat.baidu.com', timeout=60000)
        await asyncio.sleep(5)
        
        print("✅ 页面加载完成")
        
        # 发送消息前，记录页面状态
        print("\n📊 发送消息前的页面分析...")
        before_elements = await page.query_selector_all('div')
        before_texts = []
        for element in before_elements:
            try:
                text = await element.inner_text()
                if text and len(text.strip()) > 5:
                    before_texts.append(text.strip())
            except:
                pass
        
        print(f"发送前页面有 {len(before_texts)} 个有意义的文本元素")
        
        # 发送消息
        print("\n📤 发送测试消息...")
        input_element = await page.query_selector('#chat-input-box')
        if input_element:
            await input_element.click()
            await asyncio.sleep(1)
            
            test_message = "你好，请介绍一下你自己"
            await input_element.type(test_message, delay=100)
            print(f"✅ 输入消息: {test_message}")
            
            await asyncio.sleep(2)
            await page.keyboard.press('Enter')
            print("✅ 消息已发送")
            
            # 等待回复
            print("\n⏳ 等待AI回复...")
            await asyncio.sleep(10)
            
            # 分析页面变化
            print("\n📊 发送消息后的页面分析...")
            after_elements = await page.query_selector_all('div')
            after_texts = []
            for element in after_elements:
                try:
                    text = await element.inner_text()
                    if text and len(text.strip()) > 5:
                        after_texts.append(text.strip())
                except:
                    pass
            
            print(f"发送后页面有 {len(after_texts)} 个有意义的文本元素")
            
            # 找出新增的文本
            new_texts = []
            for text in after_texts:
                if text not in before_texts:
                    new_texts.append(text)
            
            print(f"\n🆕 发现 {len(new_texts)} 个新增文本:")
            for i, text in enumerate(new_texts, 1):
                print(f"[{i}] {text[:100]}{'...' if len(text) > 100 else ''}")
            
            # 尝试不同的回复选择器
            print("\n🔍 测试不同的回复选择器...")
            
            selectors_to_test = [
                # 通用聊天选择器
                '.chat-item:last-child',
                '.chat-message:last-child',
                '.message:last-child',
                '.response:last-child',
                '.answer:last-child',
                
                # 百度特定选择器
                '.chat-content',
                '.answer-content',
                '.response-content',
                '.ai-response',
                '.bot-response',
                
                # 更具体的选择器
                '[data-role="assistant"]',
                '[data-type="response"]',
                '[class*="response"]',
                '[class*="answer"]',
                '[class*="reply"]',
                
                # 最后的元素选择器
                'div:last-child',
                '.chat-item:last-of-type',
                '.message-item:last-of-type'
            ]
            
            found_responses = []
            
            for selector in selectors_to_test:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        for i, element in enumerate(elements[-3:]):  # 只检查最后3个
                            try:
                                text = await element.inner_text()
                                if text and len(text.strip()) > 0:
                                    # 过滤掉明显不是回复的文本
                                    if text.strip() not in ['提问', '发送', '搜索', '登录', '注册']:
                                        found_responses.append({
                                            'selector': selector,
                                            'index': i,
                                            'text': text.strip()[:200],
                                            'full_text': text.strip()
                                        })
                            except:
                                pass
                except:
                    pass
            
            # 去重并显示结果
            unique_responses = []
            seen_texts = set()
            
            for resp in found_responses:
                if resp['text'] not in seen_texts:
                    unique_responses.append(resp)
                    seen_texts.add(resp['text'])
            
            print(f"\n✅ 找到 {len(unique_responses)} 个可能的回复:")
            for i, resp in enumerate(unique_responses, 1):
                print(f"\n[{i}] 选择器: {resp['selector']}")
                print(f"    文本: {resp['text']}")
                if len(resp['full_text']) > 200:
                    print(f"    完整文本: {resp['full_text']}")
            
            # 尝试找到最可能的AI回复
            print(f"\n🎯 分析最可能的AI回复:")
            
            # 过滤条件：长度合理，不是用户输入，不是界面元素
            likely_responses = []
            for resp in unique_responses:
                text = resp['full_text']
                if (len(text) > 10 and  # 长度合理
                    test_message not in text and  # 不包含用户输入
                    not text.startswith(('登录', '注册', '搜索', '发送', '提问')) and  # 不是界面元素
                    len(text) < 1000):  # 不会太长
                    likely_responses.append(resp)
            
            if likely_responses:
                print(f"🎉 找到 {len(likely_responses)} 个可能的AI回复:")
                for i, resp in enumerate(likely_responses, 1):
                    print(f"\n候选回复 {i}:")
                    print(f"选择器: {resp['selector']}")
                    print(f"内容: {resp['full_text']}")
            else:
                print("❌ 未找到明显的AI回复")
            
            # 截图保存
            await page.screenshot(path="debug_response.png", full_page=True)
            print(f"\n📸 调试截图已保存: debug_response.png")
            
            # 保存页面HTML
            html_content = await page.content()
            with open("debug_response.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("💾 页面HTML已保存: debug_response.html")
            
        else:
            print("❌ 未找到输入框")
        
        # 等待用户观察
        print(f"\n⏸️  浏览器将保持打开30秒，请观察页面...")
        await asyncio.sleep(30)
        
        await browser.close()
        await playwright.stop()
        
        print("✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🐛 AI回复元素调试工具")
    print("=" * 50)
    
    try:
        asyncio.run(debug_response_elements())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")


if __name__ == "__main__":
    main()
