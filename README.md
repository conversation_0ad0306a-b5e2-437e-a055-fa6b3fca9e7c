# 百度AI聊天机器人

一个基于Python+Playwright的自动化聊天工具，可以与百度AI聊天系统进行自动化交互。

## 功能特性

- 🤖 自动化与百度AI聊天系统交互
- 💬 支持单次查询和交互式聊天模式
- 🌐 支持多种浏览器（Chromium、Firefox、WebKit）
- 🎯 智能元素定位和回复检测
- 📝 对话历史记录管理
- 🛡️ 输入验证和错误处理
- 🎨 美观的命令行界面
- ⚙️ 灵活的配置选项

## 安装依赖

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 安装Playwright浏览器：
```bash
playwright install
```

## 使用方法

### 单次查询模式
```bash
# 基本使用
python main.py --message "你好，请介绍一下人工智能"

# 使用Firefox浏览器
python main.py --browser firefox --message "Hello"

# 无头模式运行
python main.py --headless --message "测试消息"
```

### 交互式聊天模式
```bash
# 启动交互式聊天
python main.py --interactive

# 使用特定浏览器的交互模式
python main.py --browser chromium --interactive

# 无头模式交互聊天
python main.py --headless --interactive
```

### 命令行参数说明

- `-m, --message`: 要发送的消息（单次查询模式）
- `-i, --interactive`: 启动交互式聊天模式
- `--browser`: 选择浏览器类型（chromium/firefox/webkit）
- `--headless`: 无头模式运行（不显示浏览器窗口）
- `--timeout`: AI回复超时时间（秒，默认60秒）
- `--debug`: 启用调试模式
- `--version`: 显示版本信息

### 交互式命令

在交互模式下，支持以下特殊命令：
- `quit` 或 `exit`: 退出程序
- `clear`: 清空对话历史
- `history`: 查看对话历史

## 项目结构

```
apiSearch/
├── src/
│   ├── __init__.py
│   ├── baidu_chat_bot.py      # 核心聊天机器人类
│   ├── message_handler.py     # 消息处理器
│   ├── cli.py                 # 命令行接口
│   └── config.py              # 配置管理
├── tests/
│   └── test_message_handler.py
├── requirements.txt
├── main.py                    # 主入口文件
├── test_simple.py            # 简单测试脚本
└── README.md
```

## 快速开始

1. 克隆项目并安装依赖：
```bash
git clone <repository-url>
cd apiSearch
pip install -r requirements.txt
playwright install
```

2. 运行简单测试：
```bash
python test_simple.py
```

3. 开始使用：
```bash
# 单次查询
python main.py --message "你好"

# 交互式聊天
python main.py --interactive
```

## 技术实现

### 核心技术栈
- **Python 3.8+**: 主要编程语言
- **Playwright**: 浏览器自动化框架
- **asyncio**: 异步编程支持
- **Rich**: 美化命令行输出

### 关键实现特点

1. **智能元素定位**: 使用多种CSS选择器策略定位页面元素
2. **回复检测机制**: 通过DOM变化监听判断AI回复完成
3. **错误处理**: 完善的异常处理和重试机制
4. **配置管理**: 灵活的配置系统支持多种使用场景

### 页面适配策略

程序使用多种策略来适配百度聊天页面：
- 多种输入框选择器
- 多种发送按钮定位方式
- 智能回复区域识别
- 动态内容等待机制

## 配置选项

可以通过环境变量或配置文件自定义行为：

```bash
# 环境变量配置示例
export BAIDU_CHAT_URL="https://chat.baidu.com"
export BROWSER_TYPE="chromium"
export HEADLESS="false"
export DEBUG="true"
```

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装Playwright浏览器：`playwright install`
   - 检查系统权限和依赖

2. **页面元素定位失败**
   - 启用调试模式：`--debug`
   - 检查网络连接
   - 尝试不同的浏览器类型

3. **AI回复超时**
   - 增加超时时间：`--timeout 120`
   - 检查网络稳定性
   - 尝试简化消息内容

### 调试技巧

```bash
# 启用调试模式
python main.py --debug --message "测试"

# 使用有头模式观察浏览器行为
python main.py --message "测试"  # 默认显示浏览器窗口
```

## 开发指南

### 运行测试
```bash
# 运行单元测试
python -m pytest tests/

# 运行简单功能测试
python test_simple.py
```

### 扩展功能

1. **添加新的页面适配**：修改 `baidu_chat_bot.py` 中的选择器列表
2. **自定义消息处理**：扩展 `message_handler.py` 中的处理逻辑
3. **新增命令行选项**：在 `cli.py` 中添加参数解析

## 注意事项

- 本工具仅用于学习和研究目的
- 请遵守百度网站的使用条款
- 建议合理控制请求频率，避免对服务器造成压力
- 使用时请注意网络安全和隐私保护

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
