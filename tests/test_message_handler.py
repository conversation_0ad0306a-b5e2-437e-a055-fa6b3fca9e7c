"""
消息处理器测试
"""
import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.message_handler import MessageHandler


class TestMessageHandler(unittest.TestCase):
    """消息处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.handler = MessageHandler()
    
    def test_clean_input_message(self):
        """测试输入消息清理"""
        # 测试正常消息
        result = self.handler.clean_input_message("  Hello World  ")
        self.assertEqual(result, "Hello World")
        
        # 测试空消息
        result = self.handler.clean_input_message("")
        self.assertEqual(result, "")
        
        # 测试超长消息
        long_message = "a" * 2500
        result = self.handler.clean_input_message(long_message)
        self.assertTrue(len(result) <= 2003)  # 2000 + "..."
        self.assertTrue(result.endswith("..."))
    
    def test_clean_response_message(self):
        """测试回复消息清理"""
        # 测试HTML解码
        result = self.handler.clean_response_message("Hello &amp; World")
        self.assertEqual(result, "Hello & World")
        
        # 测试多余空白字符
        result = self.handler.clean_response_message("Hello    \n\n   World")
        self.assertEqual(result, "Hello World")
        
        # 测试Markdown标记
        result = self.handler.clean_response_message("**Bold** and *italic* text")
        self.assertEqual(result, "Bold and italic text")
    
    def test_validate_message(self):
        """测试消息验证"""
        # 测试有效消息
        is_valid, error = self.handler.validate_message("Hello")
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # 测试空消息
        is_valid, error = self.handler.validate_message("")
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # 测试超长消息
        long_message = "a" * 2001
        is_valid, error = self.handler.validate_message(long_message)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # 测试恶意内容
        malicious = "<script>alert('xss')</script>"
        is_valid, error = self.handler.validate_message(malicious)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_conversation_history(self):
        """测试对话历史功能"""
        # 添加对话
        self.handler.add_to_history("Hello", "Hi there!")
        self.handler.add_to_history("How are you?", "I'm fine, thanks!")
        
        # 检查历史记录
        history = self.handler.get_conversation_history()
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['user_message'], "Hello")
        self.assertEqual(history[0]['ai_response'], "Hi there!")
        
        # 清空历史
        self.handler.clear_history()
        history = self.handler.get_conversation_history()
        self.assertEqual(len(history), 0)


if __name__ == "__main__":
    unittest.main()
