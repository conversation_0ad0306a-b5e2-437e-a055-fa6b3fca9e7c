#!/usr/bin/env python3
"""
最终完整功能测试
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def final_complete_test():
    """最终完整功能测试"""
    print("🎯 最终完整功能测试")
    print("=" * 60)
    
    # 创建配置
    config = BaiduChatConfig(
        browser_type="webkit",     # 使用WebKit（效果最好）
        headless=False,            # 显示浏览器窗口
        debug=False,               # 关闭调试减少输出
        page_load_timeout=60000,   # 60秒页面加载超时
        response_timeout=90000,    # 90秒AI回复超时
        element_timeout=60000,     # 60秒元素查找超时
        typing_delay=150           # 打字延迟
    )
    
    try:
        print("🚀 正在启动百度AI聊天机器人...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 聊天机器人启动成功")
            
            # 测试对话
            conversations = [
                {
                    "user": "你好",
                    "description": "简单问候"
                },
                {
                    "user": "2+3等于几？",
                    "description": "数学计算"
                },
                {
                    "user": "请用一句话介绍Python编程语言",
                    "description": "技术问题"
                }
            ]
            
            print(f"\n📋 开始进行 {len(conversations)} 轮对话测试...")
            
            for i, conv in enumerate(conversations, 1):
                print(f"\n{'='*50}")
                print(f"第 {i} 轮对话 - {conv['description']}")
                print(f"{'='*50}")
                
                user_message = conv['user']
                print(f"👤 用户: {user_message}")
                
                try:
                    # 发送消息
                    print("🤖 AI正在思考...")
                    response = await bot.send_message_stealth(user_message)
                    
                    if response and response.strip():
                        print(f"🤖 AI: {response}")
                        
                        # 评估回复质量
                        if len(response) > 15 and "思考中" not in response:
                            print("✅ 回复质量: 良好")
                        else:
                            print("⚠️  回复质量: 需要改进")
                    else:
                        print("❌ AI: [无有效回复]")
                    
                    # 短暂等待
                    if i < len(conversations):
                        print("⏳ 等待3秒后继续...")
                        await asyncio.sleep(3)
                    
                except Exception as e:
                    print(f"❌ 第{i}轮对话失败: {e}")
                    continue
            
            # 显示完整对话历史
            print(f"\n{'='*60}")
            print("📚 完整对话历史")
            print(f"{'='*60}")
            
            history = bot.get_conversation_history()
            
            if history:
                for i, conv in enumerate(history, 1):
                    print(f"\n对话 {i}:")
                    print(f"  👤 用户: {conv['user_message']}")
                    print(f"  🤖 AI: {conv['ai_response']}")
                    print(f"  🕐 时间: {conv['timestamp'][:19]}")
                
                print(f"\n📊 统计信息:")
                print(f"  总对话数: {len(history)}")
                successful_responses = sum(1 for conv in history if len(conv['ai_response']) > 15)
                print(f"  成功回复数: {successful_responses}")
                print(f"  成功率: {successful_responses/len(history)*100:.1f}%")
                
            else:
                print("❌ 没有对话历史记录")
            
            print(f"\n{'='*60}")
            print("🎉 最终测试完成！")
            print(f"{'='*60}")
            
            # 总结
            if history and len(history) >= 2:
                print("✅ 聊天机器人功能正常")
                print("✅ 支持多轮对话")
                print("✅ 能够获取AI回复")
                print("✅ 历史记录功能正常")
                print("\n🎯 项目已经可以正常使用！")
                
                print(f"\n📖 使用方法:")
                print(f"  单次查询: python main.py --message \"你的问题\"")
                print(f"  交互聊天: python main.py --interactive")
                print(f"  调试模式: python main.py --message \"问题\" --debug")
                
            else:
                print("⚠️  部分功能可能需要进一步调试")
            
    except Exception as e:
        print(f"❌ 最终测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(final_complete_test())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
