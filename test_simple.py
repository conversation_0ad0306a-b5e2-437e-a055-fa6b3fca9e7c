#!/usr/bin/env python3
"""
简单测试脚本 - 用于快速验证功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.baidu_chat_bot import BaiduChatBot
from src.config import BaiduChatConfig


async def test_basic_functionality():
    """测试基本功能"""
    print("开始测试百度AI聊天机器人...")
    
    # 创建配置（使用有头模式便于观察）
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=30000  # 30秒超时
    )
    
    try:
        async with BaiduChatBot(config) as bot:
            print("✓ 浏览器初始化成功")
            
            # 测试发送简单消息
            test_message = "你好"
            print(f"发送测试消息: {test_message}")
            
            response = await bot.send_message(test_message)
            print(f"收到回复: {response}")
            
            # 测试对话历史
            history = bot.get_conversation_history()
            print(f"对话历史记录数: {len(history)}")
            
            print("✓ 基本功能测试完成")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    return True


async def test_multiple_messages():
    """测试多轮对话"""
    print("\n开始测试多轮对话...")
    
    config = BaiduChatConfig(headless=True, debug=False)
    
    messages = [
        "你好",
        "今天天气怎么样？",
        "谢谢你的回答"
    ]
    
    try:
        async with BaiduChatBot(config) as bot:
            for i, message in enumerate(messages, 1):
                print(f"第{i}轮 - 发送: {message}")
                response = await bot.send_message(message)
                print(f"第{i}轮 - 回复: {response[:50]}...")
                
                # 短暂等待
                await asyncio.sleep(2)
            
            print("✓ 多轮对话测试完成")
            
    except Exception as e:
        print(f"✗ 多轮对话测试失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("百度AI聊天机器人测试程序")
    print("=" * 50)
    
    try:
        # 运行基本功能测试
        result1 = asyncio.run(test_basic_functionality())
        
        if result1:
            # 运行多轮对话测试
            result2 = asyncio.run(test_multiple_messages())
            
            if result1 and result2:
                print("\n🎉 所有测试通过！")
            else:
                print("\n❌ 部分测试失败")
        else:
            print("\n❌ 基本功能测试失败，跳过后续测试")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试程序异常: {e}")


if __name__ == "__main__":
    main()
