#!/usr/bin/env python3
"""
调试版本 - 用于诊断百度聊天页面问题
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.baidu_chat_bot import BaiduChatBot
from src.config import BaiduChatConfig


async def debug_page_elements():
    """调试页面元素"""
    print("🔍 开始调试百度聊天页面...")
    
    # 创建配置（显示浏览器窗口，启用调试）
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        page_load_timeout=60000,  # 60秒超时
        element_timeout=15000     # 15秒元素查找超时
    )
    
    try:
        async with BaiduChatBot(config) as bot:
            print("✓ 浏览器初始化成功")
            print("✓ 页面已加载")
            
            # 等待页面完全加载
            print("⏳ 等待页面完全加载...")
            await asyncio.sleep(5)
            
            # 获取页面标题
            title = await bot.page.title()
            print(f"📄 页面标题: {title}")
            
            # 获取页面URL
            url = bot.page.url
            print(f"🌐 当前URL: {url}")
            
            # 截图保存
            screenshot_path = "debug_screenshot.png"
            await bot.page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 页面截图已保存: {screenshot_path}")
            
            # 查找所有可能的输入元素
            print("\n🔍 查找页面中的输入元素...")
            
            # 扩展的选择器列表
            selectors_to_check = [
                # 通用输入框
                'input[type="text"]',
                'textarea',
                'input',
                
                # 百度特定的可能选择器
                'textarea[placeholder*="请输入"]',
                'textarea[placeholder*="输入"]',
                'input[placeholder*="请输入"]',
                'input[placeholder*="输入"]',
                'textarea[placeholder*="问我"]',
                'textarea[placeholder*="聊天"]',
                
                # 常见的聊天输入框class
                '.chat-input',
                '.input-box',
                '.message-input',
                '.chat-textarea',
                '.input-area',
                '.chat-box',
                
                # ID选择器
                '#chat-input',
                '#input',
                '#message-input',
                
                # 属性选择器
                '[data-testid*="input"]',
                '[data-testid*="chat"]',
                '[role="textbox"]',
                '[contenteditable="true"]',
                
                # 百度可能的特殊选择器
                '.yc-input',
                '.textarea',
                '.input-wrap textarea',
                '.chat-input-area textarea'
            ]
            
            found_elements = []
            
            for selector in selectors_to_check:
                try:
                    elements = await bot.page.query_selector_all(selector)
                    if elements:
                        print(f"✓ 找到 {len(elements)} 个元素: {selector}")
                        for i, element in enumerate(elements):
                            # 获取元素信息
                            tag_name = await element.evaluate('el => el.tagName')
                            placeholder = await element.get_attribute('placeholder') or ''
                            class_name = await element.get_attribute('class') or ''
                            id_attr = await element.get_attribute('id') or ''
                            
                            element_info = {
                                'selector': selector,
                                'index': i,
                                'tag': tag_name,
                                'placeholder': placeholder,
                                'class': class_name,
                                'id': id_attr
                            }
                            found_elements.append(element_info)
                            
                            print(f"  [{i}] {tag_name} - placeholder: '{placeholder}' - class: '{class_name}' - id: '{id_attr}'")
                except Exception as e:
                    # 忽略查找失败的选择器
                    pass
            
            if not found_elements:
                print("❌ 未找到任何输入元素")
                
                # 尝试获取页面的所有表单元素
                print("\n🔍 查找所有表单相关元素...")
                form_elements = await bot.page.query_selector_all('form, input, textarea, button')
                print(f"📝 找到 {len(form_elements)} 个表单相关元素")
                
                for i, element in enumerate(form_elements[:10]):  # 只显示前10个
                    tag_name = await element.evaluate('el => el.tagName')
                    element_type = await element.get_attribute('type') or ''
                    placeholder = await element.get_attribute('placeholder') or ''
                    print(f"  [{i}] {tag_name} - type: '{element_type}' - placeholder: '{placeholder}'")
            
            else:
                print(f"\n✅ 总共找到 {len(found_elements)} 个可能的输入元素")
                
                # 尝试与第一个找到的输入元素交互
                if found_elements:
                    print(f"\n🧪 尝试与第一个输入元素交互...")
                    first_element = found_elements[0]
                    selector = first_element['selector']
                    
                    try:
                        element = await bot.page.query_selector(selector)
                        if element:
                            # 尝试点击和输入
                            await element.click()
                            await asyncio.sleep(1)
                            await element.type("测试消息", delay=100)
                            print("✅ 成功输入测试消息")
                            
                            # 清空输入
                            await element.clear()
                            print("✅ 成功清空输入")
                        
                    except Exception as e:
                        print(f"❌ 交互测试失败: {e}")
            
            # 等待用户观察
            print(f"\n⏸️  浏览器将保持打开状态30秒，请观察页面...")
            print("💡 你可以手动在页面上查看输入框的位置和属性")
            await asyncio.sleep(30)
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🐛 百度AI聊天机器人调试工具")
    print("=" * 50)
    
    try:
        asyncio.run(debug_page_elements())
        print("\n✅ 调试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")


if __name__ == "__main__":
    main()
