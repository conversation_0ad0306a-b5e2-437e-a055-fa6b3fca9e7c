#!/usr/bin/env python3
"""
全面调试页面元素
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.baidu_chat_bot import BaiduChatBot
from src.config import BaiduChatConfig


async def debug_full_page():
    """全面调试页面"""
    print("🔍 全面调试页面元素...")
    
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
    )
    
    try:
        async with BaiduChatBot(config) as bot:
            print("✓ 浏览器初始化成功")
            
            # 等待页面完全加载
            await asyncio.sleep(5)
            
            # 获取页面的所有文本内容
            print("\n📄 获取页面内容...")
            page_content = await bot.page.content()
            
            # 保存页面HTML到文件
            with open("debug_page_content.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            print("💾 页面HTML已保存到: debug_page_content.html")
            
            # 查找所有包含文本的div元素
            print("\n🔍 查找所有包含文本的div元素...")
            all_divs = await bot.page.query_selector_all('div')
            print(f"📊 总共找到 {len(all_divs)} 个div元素")
            
            text_divs = []
            for i, div in enumerate(all_divs):
                try:
                    text = await div.inner_text()
                    if text and len(text.strip()) > 5:  # 只显示有意义的文本
                        class_name = await div.get_attribute('class') or ''
                        id_attr = await div.get_attribute('id') or ''
                        text_divs.append({
                            'index': i,
                            'text': text.strip()[:100],
                            'class': class_name,
                            'id': id_attr
                        })
                except:
                    pass
            
            print(f"📝 找到 {len(text_divs)} 个包含文本的div元素")
            
            # 显示前20个有文本的div
            for div_info in text_divs[:20]:
                print(f"[{div_info['index']}] ID: '{div_info['id']}' Class: '{div_info['class'][:50]}...'")
                print(f"    文本: {div_info['text']}")
                print("-" * 80)
            
            # 发送消息并观察变化
            print("\n📤 发送测试消息...")
            input_element = await bot.page.query_selector('#chat-input-box')
            if input_element:
                await input_element.click()
                await input_element.type("你好", delay=100)
                await bot.page.keyboard.press('Enter')
                print("✓ 消息已发送")
                
                # 等待回复
                print("⏳ 等待15秒观察页面变化...")
                await asyncio.sleep(15)
                
                # 再次获取页面内容，比较变化
                print("\n🔄 检查页面变化...")
                new_all_divs = await bot.page.query_selector_all('div')
                print(f"📊 现在有 {len(new_all_divs)} 个div元素")
                
                new_text_divs = []
                for i, div in enumerate(new_all_divs):
                    try:
                        text = await div.inner_text()
                        if text and len(text.strip()) > 5:
                            class_name = await div.get_attribute('class') or ''
                            id_attr = await div.get_attribute('id') or ''
                            new_text_divs.append({
                                'index': i,
                                'text': text.strip()[:100],
                                'class': class_name,
                                'id': id_attr
                            })
                    except:
                        pass
                
                print(f"📝 现在有 {len(new_text_divs)} 个包含文本的div元素")
                
                # 查找新增的文本内容
                old_texts = {div['text'] for div in text_divs}
                new_texts = []
                for div_info in new_text_divs:
                    if div_info['text'] not in old_texts:
                        new_texts.append(div_info)
                
                if new_texts:
                    print(f"\n🆕 发现 {len(new_texts)} 个新的文本元素:")
                    for div_info in new_texts:
                        print(f"ID: '{div_info['id']}' Class: '{div_info['class'][:50]}...'")
                        print(f"文本: {div_info['text']}")
                        print("-" * 80)
                else:
                    print("\n❌ 未发现新的文本内容")
                
                # 截图
                await bot.page.screenshot(path="debug_full_page_after.png", full_page=True)
                print("📸 已保存截图: debug_full_page_after.png")
            
            # 等待用户观察
            print("\n⏸️  浏览器将保持打开30秒...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🐛 全面页面调试工具")
    print("=" * 50)
    
    try:
        asyncio.run(debug_full_page())
        print("\n✅ 调试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")


if __name__ == "__main__":
    main()
