#!/usr/bin/env python3
"""
测试隐蔽模式聊天机器人
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_stealth_chat():
    """测试隐蔽模式聊天功能"""
    print("🥷 测试隐蔽模式百度AI聊天机器人...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口以便观察
        debug=True,      # 启用调试
        response_timeout=45000,  # 45秒超时
        typing_delay=150  # 更慢的打字速度
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✓ 隐蔽模式浏览器初始化成功")
            
            # 测试发送简单消息
            test_message = "你好"
            print(f"📤 发送测试消息: {test_message}")
            
            response = await bot.send_message_stealth(test_message)
            print(f"📥 收到回复: {response}")
            
            # 测试对话历史
            history = bot.get_conversation_history()
            print(f"📚 对话历史记录数: {len(history)}")
            
            print("✅ 隐蔽模式测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🥷 百度AI聊天机器人隐蔽模式测试")
    print("=" * 50)
    
    try:
        asyncio.run(test_stealth_chat())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    main()
