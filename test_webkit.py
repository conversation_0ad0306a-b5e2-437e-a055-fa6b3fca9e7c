#!/usr/bin/env python3
"""
测试WebKit浏览器（类似Safari）
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from playwright.async_api import async_playwright


async def test_webkit_browser():
    """测试WebKit浏览器"""
    print("🌐 测试WebKit浏览器（类似Safari）...")
    
    playwright = await async_playwright().start()
    
    try:
        # 启动WebKit浏览器
        browser = await playwright.webkit.launch(
            headless=False,
            args=[]  # WebKit不需要特殊参数
        )
        
        # 创建上下文（模拟Safari）
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        )
        
        # 创建页面
        page = await context.new_page()
        
        print("✅ WebKit浏览器启动成功")
        
        # 访问百度聊天页面
        print("🌐 访问百度聊天页面...")
        try:
            response = await page.goto(
                'https://chat.baidu.com',
                wait_until='domcontentloaded',
                timeout=60000
            )
            
            print(f"✅ 页面响应状态: {response.status}")
            
            # 等待页面加载
            await asyncio.sleep(10)
            
            # 获取页面信息
            title = await page.title()
            url = page.url
            
            print(f"📄 页面标题: {title}")
            print(f"🌐 最终URL: {url}")
            
            # 检查页面内容
            try:
                body_text = await page.inner_text('body')
                print(f"📝 页面内容长度: {len(body_text)} 字符")
                
                if len(body_text) > 100:
                    print(f"📝 页面内容片段: {body_text[:300]}...")
                else:
                    print(f"📝 完整页面内容: {body_text}")
            except Exception as e:
                print(f"❌ 获取页面内容失败: {e}")
            
            # 查找输入框
            print("\n🔍 查找输入框...")
            input_selectors = [
                '#chat-input-box',
                '[contenteditable="true"]',
                '.cs-pc-input-custom',
                'textarea',
                'input[type="text"]',
                'input',
                '[role="textbox"]'
            ]
            
            found_any = False
            for selector in input_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"✅ 找到输入元素: {selector} ({len(elements)}个)")
                        found_any = True
                        
                        # 尝试与第一个元素交互
                        try:
                            first_element = elements[0]
                            await first_element.click()
                            await page.keyboard.type("测试消息", delay=100)
                            print(f"✅ 成功在 {selector} 中输入测试消息")
                            
                            # 清空输入
                            await page.keyboard.press('Control+a')
                            await page.keyboard.press('Delete')
                            
                        except Exception as e:
                            print(f"❌ 与 {selector} 交互失败: {e}")
                            
                except Exception as e:
                    print(f"❌ 查找 {selector} 失败: {e}")
            
            if not found_any:
                print("❌ 未找到任何输入元素")
                
                # 尝试查找所有可能的交互元素
                print("\n🔍 查找所有可能的交互元素...")
                all_inputs = await page.query_selector_all('input, textarea, [contenteditable], [role="textbox"]')
                print(f"📊 找到 {len(all_inputs)} 个可能的交互元素")
                
                for i, element in enumerate(all_inputs[:5]):  # 只检查前5个
                    try:
                        tag_name = await element.evaluate('el => el.tagName')
                        element_type = await element.get_attribute('type') or ''
                        placeholder = await element.get_attribute('placeholder') or ''
                        class_name = await element.get_attribute('class') or ''
                        id_attr = await element.get_attribute('id') or ''
                        
                        print(f"  [{i+1}] {tag_name} - type: '{element_type}' - placeholder: '{placeholder}' - class: '{class_name[:50]}' - id: '{id_attr}'")
                    except:
                        pass
            
            # 截图保存
            await page.screenshot(path="webkit_test.png", full_page=True)
            print("📸 截图已保存: webkit_test.png")
            
            # 等待用户观察
            print("\n⏸️  浏览器将保持打开30秒，请观察页面...")
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"❌ 访问页面失败: {e}")
        
        # 关闭浏览器
        await browser.close()
        await playwright.stop()
        
        print("✅ WebKit测试完成")
        
    except Exception as e:
        print(f"❌ WebKit测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 WebKit浏览器测试（类似Safari）")
    print("=" * 50)
    
    try:
        asyncio.run(test_webkit_browser())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    main()
