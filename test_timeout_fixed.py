#!/usr/bin/env python3
"""
测试修复超时问题后的聊天机器人
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_timeout_fixed():
    """测试修复超时问题后的功能"""
    print("🕐 测试修复超时问题后的百度AI聊天机器人...")
    
    # 创建配置（增加超时时间）
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        page_load_timeout=60000,   # 60秒页面加载超时
        response_timeout=60000,    # 60秒AI回复超时
        element_timeout=60000,     # 60秒元素查找超时
        typing_delay=150           # 打字延迟
    )
    
    try:
        print("🚀 正在初始化浏览器...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            try:
                # 发送消息并等待回复
                response = await bot.send_message_stealth(test_message)
                
                if response and response.strip():
                    print(f"\n🎉 成功获取AI回复:")
                    print("=" * 60)
                    print(f"用户: {test_message}")
                    print(f"AI: {response}")
                    print("=" * 60)
                else:
                    print("⚠️  获取到空回复，尝试手动检查...")
                    
                    # 截图保存当前状态
                    await bot.page.screenshot(path="current_state.png", full_page=True)
                    print("📸 当前状态截图已保存: current_state.png")
                    
                    # 检查页面上是否有文本内容
                    page_text = await bot.page.inner_text('body')
                    if page_text:
                        print(f"📄 页面内容长度: {len(page_text)} 字符")
                        # 显示页面内容的一部分
                        print(f"📄 页面内容片段: {page_text[:200]}...")
                    else:
                        print("❌ 页面没有文本内容")
                
                # 获取对话历史
                history = bot.get_conversation_history()
                print(f"\n📚 对话历史记录数: {len(history)}")
                
                if history:
                    for i, conv in enumerate(history, 1):
                        print(f"对话 {i}:")
                        print(f"  用户: {conv['user_message']}")
                        print(f"  AI: {conv['ai_response']}")
                
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
                # 保存错误信息
                await bot.page.screenshot(path="error_state.png", full_page=True)
                print("📸 错误状态截图已保存: error_state.png")
                
                # 获取页面URL
                current_url = bot.page.url
                print(f"🌐 当前页面URL: {current_url}")
                
                raise
            
            print("\n✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 百度AI聊天机器人超时修复测试")
    print("=" * 60)
    
    try:
        asyncio.run(test_timeout_fixed())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
