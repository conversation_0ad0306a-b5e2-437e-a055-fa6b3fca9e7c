#!/usr/bin/env python3
"""
使用WebKit浏览器的完整测试
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_webkit_complete():
    """使用WebKit的完整测试"""
    print("🌐 使用WebKit浏览器进行完整测试...")
    
    # 创建配置（使用WebKit）
    config = BaiduChatConfig(
        browser_type="webkit",     # 使用WebKit（类似Safari）
        headless=False,            # 显示浏览器窗口
        debug=True,                # 启用调试
        page_load_timeout=60000,   # 60秒页面加载超时
        response_timeout=60000,    # 60秒AI回复超时
        element_timeout=60000,     # 60秒元素查找超时
        typing_delay=150           # 打字延迟
    )
    
    try:
        print("🚀 正在初始化WebKit浏览器...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ WebKit浏览器初始化成功")
            
            # 测试消息
            test_message = "你好，请简单介绍一下你自己"
            print(f"📤 发送消息: {test_message}")
            
            try:
                # 发送消息并等待回复
                response = await bot.send_message_stealth(test_message)
                
                if response and response.strip():
                    print(f"\n🎉 成功获取AI回复:")
                    print("=" * 60)
                    print(f"👤 用户: {test_message}")
                    print(f"🤖 AI: {response}")
                    print("=" * 60)
                    
                    # 测试第二轮对话
                    print(f"\n📤 发送第二条消息...")
                    second_message = "谢谢你的介绍"
                    second_response = await bot.send_message_stealth(second_message)
                    
                    if second_response and second_response.strip():
                        print(f"🎉 第二轮对话成功:")
                        print(f"👤 用户: {second_message}")
                        print(f"🤖 AI: {second_response}")
                    
                else:
                    print("⚠️  获取到空回复")
                
                # 获取完整对话历史
                history = bot.get_conversation_history()
                print(f"\n📚 完整对话历史 ({len(history)} 条):")
                
                for i, conv in enumerate(history, 1):
                    print(f"\n对话 {i}:")
                    print(f"  👤 用户: {conv['user_message']}")
                    print(f"  🤖 AI: {conv['ai_response']}")
                    print(f"  🕐 时间: {conv['timestamp']}")
                
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
                # 获取当前页面信息用于调试
                try:
                    current_url = bot.page.url
                    page_title = await bot.page.title()
                    print(f"🌐 当前URL: {current_url}")
                    print(f"📄 页面标题: {page_title}")
                    
                    # 截图保存错误状态
                    await bot.page.screenshot(path="webkit_error.png", full_page=True)
                    print("📸 错误状态截图已保存: webkit_error.png")
                except:
                    pass
                
                raise
            
            print("\n✅ WebKit完整测试成功")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_interaction():
    """简单交互测试"""
    print("\n🔄 WebKit简单交互测试...")
    
    config = BaiduChatConfig(
        browser_type="webkit",
        headless=False,
        debug=False,  # 减少日志输出
        response_timeout=30000
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            messages = ["你好", "今天天气怎么样？", "谢谢"]
            
            for i, message in enumerate(messages, 1):
                print(f"\n第{i}轮对话:")
                print(f"👤 用户: {message}")
                
                try:
                    response = await bot.send_message_stealth(message)
                    if response:
                        print(f"🤖 AI: {response}")
                    else:
                        print("🤖 AI: [无回复]")
                    
                    # 短暂等待
                    await asyncio.sleep(3)
                    
                except Exception as e:
                    print(f"❌ 第{i}轮对话失败: {e}")
                    break
            
            print("\n📊 最终对话历史:")
            history = bot.get_conversation_history()
            for i, conv in enumerate(history, 1):
                print(f"{i}. 👤: {conv['user_message']}")
                print(f"   🤖: {conv['ai_response']}")
                
    except Exception as e:
        print(f"❌ 简单交互测试失败: {e}")


def main():
    """主函数"""
    print("🧪 WebKit浏览器完整功能测试")
    print("=" * 60)
    
    try:
        # 运行完整测试
        asyncio.run(test_webkit_complete())
        
        # 可选：运行简单交互测试
        # print("\n" + "="*60)
        # asyncio.run(test_simple_interaction())
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
